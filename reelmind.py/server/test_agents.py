#!/usr/bin/env python3
"""
Test script for audio and video agents
"""

import asyncio
import sys
import os

# Add the server directory to the Python path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from tools.audio_generators import generate_audio
from tools.video_generators import generate_video
from langchain_core.runnables import RunnableConfig


async def test_audio_agent():
    """Test the audio generation agent"""
    print("🎵 Testing Audio Agent...")
    
    # Mock configuration
    config = RunnableConfig(configurable={
        'canvas_id': 'test_canvas',
        'session_id': 'test_session',
        'model_info': {
            'audio': {
                'model': 'eleven_multilingual_v2',
                'provider': 'elevenlabs'
            }
        }
    })
    
    try:
        # Test TTS generation
        result = await generate_audio(
            text="Hello, this is a test of the audio generation system.",
            audio_type="tts",
            config=config,
            tool_call_id="test_audio_call_1"
        )
        print(f"✅ Audio TTS generation result: {result}")
        
        # Test sound effect generation
        result = await generate_audio(
            text="Birds chirping in a forest",
            audio_type="sound_effect",
            config=config,
            tool_call_id="test_audio_call_2",
            duration=3.0
        )
        print(f"✅ Audio sound effect generation result: {result}")
        
    except Exception as e:
        print(f"❌ Audio agent test failed: {e}")


async def test_video_agent():
    """Test the video generation agent"""
    print("🎥 Testing Video Agent...")
    
    # Mock configuration
    config = RunnableConfig(configurable={
        'canvas_id': 'test_canvas',
        'session_id': 'test_session',
        'model_info': {
            'video': {
                'model': 'minimax/video-01',
                'provider': 'replicate'
            }
        }
    })
    
    try:
        # Test text-to-video generation
        result = await generate_video(
            prompt="A cat playing with a ball of yarn in slow motion",
            aspect_ratio="16:9",
            config=config,
            tool_call_id="test_video_call_1",
            duration=5.0
        )
        print(f"✅ Video generation result: {result}")
        
    except Exception as e:
        print(f"❌ Video agent test failed: {e}")


async def main():
    """Main test function"""
    print("🚀 Starting Agent Tests...")
    
    # Test audio agent
    await test_audio_agent()
    
    # Test video agent  
    await test_video_agent()
    
    print("✨ Agent tests completed!")


if __name__ == "__main__":
    asyncio.run(main())
