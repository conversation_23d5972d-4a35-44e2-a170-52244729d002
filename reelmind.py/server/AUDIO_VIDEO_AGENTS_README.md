# Audio and Video Agents for ReelMind.py

This document describes the newly added audio and video generation agents that extend the ReelMind.py server functionality.

## Overview

The audio and video agents follow the same pattern as the existing `image_generators.py` and `write_plan.py` agents, providing seamless integration with the LangGraph-based agent system.

## Audio Agent (`audio_generators.py`)

### Features
- **Text-to-Speech (TTS)**: Convert text to natural-sounding speech
- **Sound Effects**: Generate sound effects from text descriptions
- **Multiple Providers**: Support for ElevenLabs, OpenAI, and Replicate

### Supported Providers

#### ElevenLabs
- **Models**: `eleven_multilingual_v2`, `eleven_turbo_v2_5`
- **Features**: High-quality TTS with multiple voices, sound effect generation
- **Voices**: <PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, <PERSON>
- **Configuration**: Requires `elevenlabs.api_key` in config

#### OpenAI
- **Models**: `tts-1`, `tts-1-hd`
- **Features**: TTS only (no sound effects)
- **Voices**: alloy, echo, fable, onyx, nova, shimmer
- **Configuration**: Requires `openai.api_key` in config

#### Replicate
- **Models**: Various audio models (MusicGen, Bark, XTTS v2)
- **Features**: Music generation, TTS with voice cloning
- **Configuration**: Requires `replicate.api_key` in config

### Usage Example
```python
# Text-to-Speech
result = await generate_audio(
    text="Hello, this is a test message",
    audio_type="tts",
    voice_id="21m00Tcm4TlvDq8ikWAM",  # Rachel voice
    config=config,
    tool_call_id="audio_call_1"
)

# Sound Effect
result = await generate_audio(
    text="Birds chirping in a forest",
    audio_type="sound_effect", 
    duration=5.0,
    config=config,
    tool_call_id="audio_call_2"
)
```

## Video Agent (`video_generators.py`)

### Features
- **Text-to-Video**: Generate videos from text descriptions
- **Image-to-Video**: Animate static images
- **Multiple Providers**: Support for Replicate, Runway ML, and Stability AI
- **Flexible Aspect Ratios**: 1:1, 16:9, 4:3, 3:4, 9:16

### Supported Providers

#### Replicate
- **Models**: `minimax/video-01`, `lucataco/animate-diff`, `stability-ai/stable-video-diffusion`, `wan-video/wan-2.1-1.3b`
- **Features**: Text-to-video, image-to-video, various quality levels
- **Configuration**: Requires `replicate.api_key` in config

#### Runway ML
- **Models**: `gen3a_turbo`, `gen3a`, `gen2`
- **Features**: High-quality video generation, motion control
- **Duration**: 2-10 seconds
- **Configuration**: Requires `runway.api_key` in config

#### Stability AI
- **Models**: `stable-video-diffusion-1-1`, `stable-video-diffusion-1-0`
- **Features**: Image-to-video generation with motion control
- **Configuration**: Requires `stability.api_key` in config

### Usage Example
```python
# Text-to-Video
result = await generate_video(
    prompt="A cat playing with a ball of yarn",
    aspect_ratio="16:9",
    duration=5.0,
    config=config,
    tool_call_id="video_call_1"
)

# Image-to-Video
result = await generate_video(
    prompt="Add gentle motion to this image",
    aspect_ratio="16:9",
    input_image="im_jurheut7.png",
    motion_strength=0.7,
    config=config,
    tool_call_id="video_call_2"
)
```

## Integration with LangGraph

The agents are automatically integrated into the LangGraph service:

### Updated `langgraph_service.py`
- Added imports for `generate_audio` and `generate_video`
- Updated tool mapping to include new agents
- Enhanced agent context to support audio and video models
- Updated agent prompt to reflect multimedia capabilities

### Agent Configuration
```python
# Example agent call with multimedia support
await langgraph_agent(
    messages=messages,
    canvas_id=canvas_id,
    session_id=session_id,
    text_model=text_model,
    image_model=image_model,
    audio_model=audio_model,  # New parameter
    video_model=video_model   # New parameter
)
```

## Canvas Integration

Both agents integrate seamlessly with the canvas system:

### Audio Elements
- **Type**: `audio`
- **Dimensions**: 300x60 pixels (default)
- **File Format**: MP3
- **Positioning**: Auto-positioned relative to existing audio elements

### Video Elements  
- **Type**: `video`
- **Dimensions**: Based on aspect ratio and model
- **File Format**: MP4
- **Positioning**: Auto-positioned relative to existing video elements

## Configuration Requirements

Add the following to your `config.toml`:

```toml
[elevenlabs]
api_key = "your_elevenlabs_api_key"

[openai]
api_key = "your_openai_api_key"

[replicate]
api_key = "your_replicate_api_key"

[runway]
api_key = "your_runway_api_key"

[stability]
api_key = "your_stability_api_key"
```

## File Structure

```
reelmind.py/server/tools/
├── audio_generators.py              # Main audio agent
├── audio_generators/
│   ├── __init__.py
│   ├── base.py                      # Base audio generator class
│   ├── elevenlabs.py               # ElevenLabs implementation
│   ├── openai.py                   # OpenAI implementation
│   └── replicate.py                # Replicate implementation
├── video_generators.py              # Main video agent
└── video_generators/
    ├── __init__.py
    ├── base.py                      # Base video generator class
    ├── replicate.py                # Replicate implementation
    ├── runway.py                   # Runway ML implementation
    └── stability.py                # Stability AI implementation
```

## Testing

Run the test script to verify functionality:

```bash
cd reelmind.py/server
python test_agents.py
```

## Error Handling

Both agents include comprehensive error handling:
- API key validation
- Provider-specific error messages
- Graceful fallbacks
- WebSocket error notifications
- Detailed logging

## Future Enhancements

Potential improvements:
- Voice cloning support
- Advanced video editing capabilities
- Real-time audio/video streaming
- Batch processing
- Custom model fine-tuning
- Integration with more providers
