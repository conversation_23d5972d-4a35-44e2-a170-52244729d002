import os
import aiofiles
from typing import <PERSON><PERSON>, Dict, Any, Optional
from nanoid import generate

from .base import BaseAudioGenerator
from services.config_service import config_service, FILES_DIR
from utils.http_client import HttpClient


class OpenAIAudioGenerator(BaseAudioGenerator):
    """OpenAI audio generator for TTS"""
    
    def __init__(self):
        self.base_url = "https://api.openai.com/v1"
        
    async def generate(
        self,
        text: str,
        model: str,
        audio_type: str,
        voice: str = "alloy",
        **kwargs
    ) -> Tuple[str, str]:
        """Generate audio using OpenAI TTS API"""
        
        if audio_type != 'tts':
            raise ValueError("OpenAI generator only supports text-to-speech (tts)")
            
        api_key = config_service.app_config.get('openai', {}).get('api_key', '')
        if not api_key:
            raise ValueError("OpenAI API key is not configured")
            
        url = f"{self.base_url}/audio/speech"
        headers = {
            "Authorization": f"Bearer {api_key}",
            "Content-Type": "application/json"
        }
        
        data = {
            "model": model or "tts-1",
            "input": text,
            "voice": voice,
            "response_format": "mp3"
        }
        
        async with HttpClient.create() as client:
            response = await client.post(url, headers=headers, json=data)
            
            if response.status_code != 200:
                error_text = response.text
                raise Exception(f"OpenAI TTS failed: {error_text}")
            
            # Generate filename and save audio
            audio_id = 'au_' + generate(size=8)
            filename = f"{audio_id}.mp3"
            file_path = os.path.join(FILES_DIR, filename)
            
            async with aiofiles.open(file_path, 'wb') as f:
                await f.write(response.content)
            
            return "audio/mpeg", filename
    
    def get_supported_models(self) -> Dict[str, Any]:
        """Get supported OpenAI models"""
        return {
            "tts-1": {
                "name": "TTS-1",
                "description": "Standard quality text-to-speech model",
                "type": "tts"
            },
            "tts-1-hd": {
                "name": "TTS-1 HD",
                "description": "High definition text-to-speech model",
                "type": "tts"
            }
        }
    
    def get_supported_voices(self) -> Dict[str, Any]:
        """Get supported OpenAI voices"""
        return {
            "alloy": {
                "name": "Alloy",
                "description": "Neutral voice",
                "gender": "neutral",
                "language": "en"
            },
            "echo": {
                "name": "Echo",
                "description": "Male voice",
                "gender": "male",
                "language": "en"
            },
            "fable": {
                "name": "Fable",
                "description": "British male voice",
                "gender": "male",
                "language": "en"
            },
            "onyx": {
                "name": "Onyx",
                "description": "Male voice",
                "gender": "male",
                "language": "en"
            },
            "nova": {
                "name": "Nova",
                "description": "Female voice",
                "gender": "female",
                "language": "en"
            },
            "shimmer": {
                "name": "Shimmer",
                "description": "Female voice",
                "gender": "female",
                "language": "en"
            }
        }
