import os
import asyncio
import aiofiles
from typing import <PERSON><PERSON>, Dict, Any, Optional
from nanoid import generate

from .base import BaseAudioGenerator
from services.config_service import config_service, FILES_DIR
from utils.http_client import HttpClient


class ReplicateAudioGenerator(BaseAudioGenerator):
    """Replicate audio generator for various audio models"""
    
    def __init__(self):
        self.base_url = "https://api.replicate.com/v1"
        
    async def generate(
        self,
        text: str,
        model: str,
        audio_type: str,
        duration: Optional[float] = 5.0,
        **kwargs
    ) -> Tuple[str, str]:
        """Generate audio using Replicate API"""
        
        api_key = config_service.app_config.get('replicate', {}).get('api_key', '')
        if not api_key:
            raise ValueError("Replicate API key is not configured")
            
        headers = {
            "Authorization": f"Bearer {api_key}",
            "Content-Type": "application/json"
        }
        
        # Prepare input based on audio type
        if audio_type == 'sound_effect':
            input_data = {
                "prompt": text,
                "duration": duration or 5.0,
                "output_format": "mp3"
            }
        elif audio_type == 'tts':
            input_data = {
                "text": text,
                "output_format": "mp3"
            }
        else:
            raise ValueError(f"Unsupported audio type: {audio_type}")
        
        # Use default model if not specified
        if not model:
            model = "meta/musicgen:671ac645ce5e552cc63a54a2bbff63fcf798043055d2dac5fc9e36a837eedcfb"
        
        url = f"{self.base_url}/models/{model}/predictions"
        
        data = {
            "input": input_data
        }
        
        async with HttpClient.create() as client:
            # Step 1: Initial POST request
            response = await client.post(url, headers=headers, json=data)
            res = response.json()
            
            prediction_id = res.get("id")
            status = res.get("status")
            print(f'🎵 Initial prediction status: {status}, id: {prediction_id}')
            
            if not prediction_id:
                print('🎵 Full Replicate response:', res)
                raise Exception("Replicate API returned no prediction id")
            
            # Step 2: Polling loop
            polling_url = f"{self.base_url}/predictions/{prediction_id}"
            
            while status not in ("succeeded", "failed", "canceled"):
                print(f'🎵 Polling prediction {prediction_id}, current status: {status} ...')
                await asyncio.sleep(3)  # Wait 3 seconds between polls
                
                poll_response = await client.get(polling_url, headers=headers)
                poll_res = poll_response.json()
                
                status = poll_res.get("status")
                output = poll_res.get("output", None)
            
            # Step 3: Final check
            if status != "succeeded" or not output:
                detail_error = poll_res.get('detail', f'Prediction failed with status: {status}')
                raise Exception(f'Replicate audio generation failed: {detail_error}')
            
            print(f'🎵 Prediction succeeded, output url: {output}')
            
            # Download the generated audio
            if isinstance(output, list) and len(output) > 0:
                audio_url = output[0]
            elif isinstance(output, str):
                audio_url = output
            else:
                raise Exception("Invalid output format from Replicate")
            
            # Download and save the audio file
            audio_response = await client.get(audio_url)
            if audio_response.status_code != 200:
                raise Exception(f"Failed to download audio from {audio_url}")
            
            # Generate filename and save audio
            audio_id = 'au_' + generate(size=8)
            filename = f"{audio_id}.mp3"
            file_path = os.path.join(FILES_DIR, filename)
            
            async with aiofiles.open(file_path, 'wb') as f:
                await f.write(audio_response.content)
            
            return "audio/mpeg", filename
    
    def get_supported_models(self) -> Dict[str, Any]:
        """Get supported Replicate models"""
        return {
            "meta/musicgen": {
                "name": "MusicGen",
                "description": "Generate music from text descriptions",
                "type": "sound_effect"
            },
            "suno-ai/bark": {
                "name": "Bark",
                "description": "Text-to-speech with realistic voices",
                "type": "tts"
            },
            "lucataco/xtts-v2": {
                "name": "XTTS v2",
                "description": "Cross-lingual text-to-speech",
                "type": "tts"
            }
        }
    
    def get_supported_voices(self) -> Dict[str, Any]:
        """Get supported voices (model-dependent)"""
        return {
            "default": {
                "name": "Default",
                "description": "Default voice for the model",
                "gender": "neutral",
                "language": "en"
            }
        }
