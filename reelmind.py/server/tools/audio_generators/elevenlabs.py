import os
import aiofiles
from typing import <PERSON><PERSON>, Dict, Any, Optional
from nanoid import generate

from .base import BaseAudioGenerator
from services.config_service import config_service, FILES_DIR
from utils.http_client import HttpClient


class ElevenLabsGenerator(BaseAudioGenerator):
    """ElevenLabs audio generator for TTS and sound effects"""
    
    def __init__(self):
        self.base_url = "https://api.elevenlabs.io/v1"
        
    async def generate(
        self,
        text: str,
        model: str,
        audio_type: str,
        voice_id: Optional[str] = None,
        duration: Optional[float] = 5.0,
        **kwargs
    ) -> Tuple[str, str]:
        """Generate audio using ElevenLabs API"""
        
        api_key = config_service.app_config.get('elevenlabs', {}).get('api_key', '')
        if not api_key:
            raise ValueError("ElevenLabs API key is not configured")
            
        headers = {
            "Accept": "audio/mpeg",
            "Content-Type": "application/json",
            "xi-api-key": api_key
        }
        
        if audio_type == 'tts':
            return await self._generate_tts(text, model, voice_id, headers)
        elif audio_type == 'sound_effect':
            return await self._generate_sound_effect(text, duration, headers)
        else:
            raise ValueError(f"Unsupported audio type: {audio_type}")
    
    async def _generate_tts(
        self, 
        text: str, 
        model: str, 
        voice_id: Optional[str], 
        headers: Dict[str, str]
    ) -> Tuple[str, str]:
        """Generate text-to-speech audio"""
        
        # Use default voice if not specified
        if not voice_id:
            voice_id = "21m00Tcm4TlvDq8ikWAM"  # Rachel voice
            
        url = f"{self.base_url}/text-to-speech/{voice_id}"
        
        data = {
            "text": text,
            "model_id": model or "eleven_multilingual_v2",
            "voice_settings": {
                "stability": 0.5,
                "similarity_boost": 0.75,
                "style": 0.0,
                "use_speaker_boost": True
            }
        }
        
        async with HttpClient.create() as client:
            response = await client.post(url, headers=headers, json=data)
            
            if response.status_code != 200:
                error_text = response.text
                raise Exception(f"ElevenLabs TTS failed: {error_text}")
            
            # Generate filename and save audio
            audio_id = 'au_' + generate(size=8)
            filename = f"{audio_id}.mp3"
            file_path = os.path.join(FILES_DIR, filename)
            
            async with aiofiles.open(file_path, 'wb') as f:
                await f.write(response.content)
            
            return "audio/mpeg", filename
    
    async def _generate_sound_effect(
        self, 
        text: str, 
        duration: float, 
        headers: Dict[str, str]
    ) -> Tuple[str, str]:
        """Generate sound effect audio"""
        
        url = f"{self.base_url}/sound-generation"
        
        data = {
            "text": text,
            "duration_seconds": duration,
            "prompt_influence": 0.3
        }
        
        async with HttpClient.create() as client:
            response = await client.post(url, headers=headers, json=data)
            
            if response.status_code != 200:
                error_text = response.text
                raise Exception(f"ElevenLabs sound effect generation failed: {error_text}")
            
            # Generate filename and save audio
            audio_id = 'au_' + generate(size=8)
            filename = f"{audio_id}.mp3"
            file_path = os.path.join(FILES_DIR, filename)
            
            async with aiofiles.open(file_path, 'wb') as f:
                await f.write(response.content)
            
            return "audio/mpeg", filename
    
    def get_supported_models(self) -> Dict[str, Any]:
        """Get supported ElevenLabs models"""
        return {
            "eleven_multilingual_v2": {
                "name": "Multilingual v2",
                "description": "High quality multilingual model",
                "type": "tts"
            },
            "eleven_turbo_v2_5": {
                "name": "Turbo v2.5", 
                "description": "Fast generation model",
                "type": "tts"
            }
        }
    
    def get_supported_voices(self) -> Dict[str, Any]:
        """Get supported ElevenLabs voices"""
        return {
            "21m00Tcm4TlvDq8ikWAM": {
                "name": "Rachel",
                "description": "American female voice",
                "gender": "female",
                "language": "en"
            },
            "AZnzlk1XvdvUeBnXmlld": {
                "name": "Domi",
                "description": "American female voice",
                "gender": "female", 
                "language": "en"
            },
            "EXAVITQu4vr4xnSDxMaL": {
                "name": "Bella",
                "description": "American female voice",
                "gender": "female",
                "language": "en"
            },
            "ErXwobaYiN019PkySvjV": {
                "name": "Antoni",
                "description": "American male voice",
                "gender": "male",
                "language": "en"
            },
            "MF3mGyEYCl7XYWbV9V6O": {
                "name": "Elli",
                "description": "American female voice", 
                "gender": "female",
                "language": "en"
            },
            "TxGEqnHWrfWFTfGW9XjX": {
                "name": "Josh",
                "description": "American male voice",
                "gender": "male",
                "language": "en"
            }
        }
