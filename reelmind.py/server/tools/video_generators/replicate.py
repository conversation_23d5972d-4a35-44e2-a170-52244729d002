import os
import asyncio
import aiofiles
import mimetypes
from typing import <PERSON><PERSON>, Dict, Any, Optional
from nanoid import generate

from .base import BaseVideoGenerator
from services.config_service import config_service, FILES_DIR
from utils.http_client import HttpClient


class ReplicateVideoGenerator(BaseVideoGenerator):
    """Replicate video generator for various video models"""
    
    def __init__(self):
        self.base_url = "https://api.replicate.com/v1"
        
    async def generate(
        self,
        prompt: str,
        model: str,
        aspect_ratio: str = "16:9",
        duration: Optional[float] = 5.0,
        input_image: Optional[str] = None,
        **kwargs
    ) -> Tuple[str, int, int, str]:
        """Generate video using Replicate API"""
        
        api_key = config_service.app_config.get('replicate', {}).get('api_key', '')
        if not api_key:
            raise ValueError("Replicate API key is not configured")
            
        headers = {
            "Authorization": f"Bearer {api_key}",
            "Content-Type": "application/json"
        }
        
        # Prepare input data
        input_data = {
            "prompt": prompt,
            "aspect_ratio": aspect_ratio,
        }
        
        # Add duration if supported by model
        if duration and "runway" not in model.lower():
            input_data["duration"] = duration
            
        # Add input image if provided
        if input_image:
            input_data["image"] = input_image
        
        # Use default model if not specified
        if not model:
            model = "minimax/video-01"
        
        url = f"{self.base_url}/models/{model}/predictions"
        
        data = {
            "input": input_data
        }
        
        async with HttpClient.create() as client:
            # Step 1: Initial POST request
            response = await client.post(url, headers=headers, json=data)
            res = response.json()
            
            prediction_id = res.get("id")
            status = res.get("status")
            print(f'🎥 Initial prediction status: {status}, id: {prediction_id}')
            
            if not prediction_id:
                print('🎥 Full Replicate response:', res)
                raise Exception("Replicate API returned no prediction id")
            
            # Step 2: Polling loop
            polling_url = f"{self.base_url}/predictions/{prediction_id}"
            
            while status not in ("succeeded", "failed", "canceled"):
                print(f'🎥 Polling prediction {prediction_id}, current status: {status} ...')
                await asyncio.sleep(5)  # Wait 5 seconds between polls for video
                
                poll_response = await client.get(polling_url, headers=headers)
                poll_res = poll_response.json()
                
                status = poll_res.get("status")
                output = poll_res.get("output", None)
            
            # Step 3: Final check
            if status != "succeeded" or not output:
                detail_error = poll_res.get('detail', f'Prediction failed with status: {status}')
                raise Exception(f'Replicate video generation failed: {detail_error}')
            
            print(f'🎥 Prediction succeeded, output: {output}')
            
            # Get video URL from output
            if isinstance(output, list) and len(output) > 0:
                video_url = output[0]
            elif isinstance(output, str):
                video_url = output
            elif isinstance(output, dict) and 'video' in output:
                video_url = output['video']
            else:
                raise Exception("Invalid output format from Replicate")
            
            # Download and save the video file
            return await self._download_and_save_video(video_url, client)
    
    async def _download_and_save_video(self, video_url: str, client) -> Tuple[str, int, int, str]:
        """Download video and save to local storage"""
        
        video_response = await client.get(video_url)
        if video_response.status_code != 200:
            raise Exception(f"Failed to download video from {video_url}")
        
        # Generate filename and save video
        video_id = 'vi_' + generate(size=8)
        filename = f"{video_id}.mp4"
        file_path = os.path.join(FILES_DIR, filename)
        
        async with aiofiles.open(file_path, 'wb') as f:
            await f.write(video_response.content)
        
        print(f'🎥 Video saved to {file_path}')
        
        # Get video dimensions (default values, could be enhanced with ffprobe)
        width, height = self._get_dimensions_from_aspect_ratio("16:9")
        mime_type = mimetypes.types_map.get('.mp4', 'video/mp4')
        
        return mime_type, width, height, filename
    
    def _get_dimensions_from_aspect_ratio(self, aspect_ratio: str) -> Tuple[int, int]:
        """Get width and height from aspect ratio"""
        aspect_ratios = self.get_supported_aspect_ratios()
        if aspect_ratio in aspect_ratios:
            return aspect_ratios[aspect_ratio]["width"], aspect_ratios[aspect_ratio]["height"]
        return 1920, 1080  # Default to 16:9
    
    def get_supported_models(self) -> Dict[str, Any]:
        """Get supported Replicate models"""
        return {
            "minimax/video-01": {
                "name": "MiniMax Video-01",
                "description": "High-quality text-to-video generation",
                "type": "text2video",
                "supports_image_input": True
            },
            "lucataco/animate-diff": {
                "name": "AnimateDiff",
                "description": "Animate images with motion",
                "type": "image2video",
                "supports_image_input": True
            },
            "stability-ai/stable-video-diffusion": {
                "name": "Stable Video Diffusion",
                "description": "Generate videos from images",
                "type": "image2video",
                "supports_image_input": True
            },
            "wan-video/wan-2.1-1.3b": {
                "name": "WAN Video 2.1",
                "description": "Fast video generation model",
                "type": "text2video",
                "supports_image_input": False
            }
        }
