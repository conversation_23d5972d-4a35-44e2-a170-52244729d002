import os
import asyncio
import aiofiles
import mimetypes
from typing import <PERSON><PERSON>, Dict, Any, Optional
from nanoid import generate

from .base import BaseVideoGenerator
from services.config_service import config_service, FILES_DIR
from utils.http_client import HttpClient


class RunwayVideoGenerator(BaseVideoGenerator):
    """Runway ML video generator"""
    
    def __init__(self):
        self.base_url = "https://api.runwayml.com/v1"
        
    async def generate(
        self,
        prompt: str,
        model: str,
        duration: Optional[float] = 5.0,
        motion_strength: Optional[float] = 0.5,
        input_image: Optional[str] = None,
        **kwargs
    ) -> Tuple[str, int, int, str]:
        """Generate video using Runway API"""
        
        api_key = config_service.app_config.get('runway', {}).get('api_key', '')
        if not api_key:
            raise ValueError("Runway API key is not configured")
            
        headers = {
            "Authorization": f"Bearer {api_key}",
            "Content-Type": "application/json"
        }
        
        # Prepare input data
        input_data = {
            "text_prompt": prompt,
            "duration": min(max(duration or 5.0, 2), 10),  # Clamp between 2-10 seconds
            "motion_strength": min(max(motion_strength or 0.5, 0), 1),  # Clamp between 0-1
        }
        
        # Add input image if provided
        if input_image:
            # For Runway, we might need to upload the image first
            input_data["image_prompt"] = input_image
        
        # Use default model if not specified
        if not model:
            model = "gen3a_turbo"
        
        url = f"{self.base_url}/image_to_video" if input_image else f"{self.base_url}/text_to_video"
        
        data = {
            "model": model,
            **input_data
        }
        
        async with HttpClient.create() as client:
            # Step 1: Initial POST request
            response = await client.post(url, headers=headers, json=data)
            res = response.json()
            
            task_id = res.get("id")
            status = res.get("status")
            print(f'🎥 Initial Runway task status: {status}, id: {task_id}')
            
            if not task_id:
                print('🎥 Full Runway response:', res)
                raise Exception("Runway API returned no task id")
            
            # Step 2: Polling loop
            polling_url = f"{self.base_url}/tasks/{task_id}"
            
            while status not in ("SUCCEEDED", "FAILED", "CANCELLED"):
                print(f'🎥 Polling Runway task {task_id}, current status: {status} ...')
                await asyncio.sleep(5)  # Wait 5 seconds between polls
                
                poll_response = await client.get(polling_url, headers=headers)
                poll_res = poll_response.json()
                
                status = poll_res.get("status")
                output = poll_res.get("output", None)
            
            # Step 3: Final check
            if status != "SUCCEEDED" or not output:
                detail_error = poll_res.get('failure_reason', f'Task failed with status: {status}')
                raise Exception(f'Runway video generation failed: {detail_error}')
            
            print(f'🎥 Runway task succeeded, output: {output}')
            
            # Get video URL from output
            if isinstance(output, list) and len(output) > 0:
                video_url = output[0]
            elif isinstance(output, str):
                video_url = output
            elif isinstance(output, dict) and 'url' in output:
                video_url = output['url']
            else:
                raise Exception("Invalid output format from Runway")
            
            # Download and save the video file
            return await self._download_and_save_video(video_url, client)
    
    async def _download_and_save_video(self, video_url: str, client) -> Tuple[str, int, int, str]:
        """Download video and save to local storage"""
        
        video_response = await client.get(video_url)
        if video_response.status_code != 200:
            raise Exception(f"Failed to download video from {video_url}")
        
        # Generate filename and save video
        video_id = 'vi_' + generate(size=8)
        filename = f"{video_id}.mp4"
        file_path = os.path.join(FILES_DIR, filename)
        
        async with aiofiles.open(file_path, 'wb') as f:
            await f.write(video_response.content)
        
        print(f'🎥 Video saved to {file_path}')
        
        # Default dimensions for Runway videos
        width, height = 1280, 768  # Runway's default resolution
        mime_type = mimetypes.types_map.get('.mp4', 'video/mp4')
        
        return mime_type, width, height, filename
    
    def get_supported_models(self) -> Dict[str, Any]:
        """Get supported Runway models"""
        return {
            "gen3a_turbo": {
                "name": "Gen-3 Alpha Turbo",
                "description": "Fast video generation with good quality",
                "type": "text2video",
                "supports_image_input": True,
                "max_duration": 10
            },
            "gen3a": {
                "name": "Gen-3 Alpha",
                "description": "High-quality video generation",
                "type": "text2video", 
                "supports_image_input": True,
                "max_duration": 10
            },
            "gen2": {
                "name": "Gen-2",
                "description": "Previous generation model",
                "type": "text2video",
                "supports_image_input": True,
                "max_duration": 4
            }
        }
