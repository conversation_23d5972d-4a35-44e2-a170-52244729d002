import os
import asyncio
import aiofiles
import mimetypes
from typing import <PERSON><PERSON>, Dict, Any, Optional
from nanoid import generate

from .base import BaseVideoGenerator
from services.config_service import config_service, FILES_DIR
from utils.http_client import HttpClient


class StabilityVideoGenerator(BaseVideoGenerator):
    """Stability AI video generator"""
    
    def __init__(self):
        self.base_url = "https://api.stability.ai/v2beta"
        
    async def generate(
        self,
        prompt: str,
        model: str,
        aspect_ratio: str = "16:9",
        motion_strength: Optional[float] = 0.5,
        **kwargs
    ) -> Tuple[str, int, int, str]:
        """Generate video using Stability AI API"""
        
        api_key = config_service.app_config.get('stability', {}).get('api_key', '')
        if not api_key:
            raise ValueError("Stability AI API key is not configured")
            
        headers = {
            "Authorization": f"Bearer {api_key}",
            "Content-Type": "application/json"
        }
        
        # Use default model if not specified
        if not model:
            model = "stable-video-diffusion-1-1"
        
        # Prepare input data
        input_data = {
            "prompt": prompt,
            "aspect_ratio": aspect_ratio,
            "motion_bucket_id": int(motion_strength * 255) if motion_strength else 127,  # Convert to 0-255 range
        }
        
        url = f"{self.base_url}/image-to-video"
        
        data = input_data
        
        async with HttpClient.create() as client:
            # Step 1: Initial POST request
            response = await client.post(url, headers=headers, json=data)
            
            if response.status_code == 200:
                # Synchronous response
                video_content = response.content
                return await self._save_video_content(video_content)
            else:
                # Asynchronous response - get generation ID
                res = response.json()
                generation_id = res.get("id")
                
                if not generation_id:
                    print('🎥 Full Stability response:', res)
                    raise Exception("Stability AI API returned no generation id")
                
                # Step 2: Polling loop
                polling_url = f"{self.base_url}/image-to-video/result/{generation_id}"
                
                while True:
                    print(f'🎥 Polling Stability generation {generation_id} ...')
                    await asyncio.sleep(3)  # Wait 3 seconds between polls
                    
                    poll_response = await client.get(polling_url, headers=headers)
                    
                    if poll_response.status_code == 200:
                        # Generation complete
                        video_content = poll_response.content
                        return await self._save_video_content(video_content)
                    elif poll_response.status_code == 202:
                        # Still processing
                        continue
                    else:
                        # Error
                        error_res = poll_response.json()
                        raise Exception(f'Stability video generation failed: {error_res}')
    
    async def _save_video_content(self, video_content: bytes) -> Tuple[str, int, int, str]:
        """Save video content to local storage"""
        
        # Generate filename and save video
        video_id = 'vi_' + generate(size=8)
        filename = f"{video_id}.mp4"
        file_path = os.path.join(FILES_DIR, filename)
        
        async with aiofiles.open(file_path, 'wb') as f:
            await f.write(video_content)
        
        print(f'🎥 Video saved to {file_path}')
        
        # Default dimensions for Stability videos
        width, height = 1024, 576  # Stability's default resolution
        mime_type = mimetypes.types_map.get('.mp4', 'video/mp4')
        
        return mime_type, width, height, filename
    
    def get_supported_models(self) -> Dict[str, Any]:
        """Get supported Stability AI models"""
        return {
            "stable-video-diffusion-1-1": {
                "name": "Stable Video Diffusion 1.1",
                "description": "Latest stable video diffusion model",
                "type": "image2video",
                "supports_image_input": True
            },
            "stable-video-diffusion-1-0": {
                "name": "Stable Video Diffusion 1.0",
                "description": "Original stable video diffusion model",
                "type": "image2video",
                "supports_image_input": True
            }
        }
