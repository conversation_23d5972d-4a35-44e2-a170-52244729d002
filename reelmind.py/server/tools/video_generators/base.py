from abc import ABC, abstractmethod
from typing import <PERSON><PERSON>, Optional, Dict, Any


class BaseVideoGenerator(ABC):
    """Base class for all video generators"""
    
    @abstractmethod
    async def generate(
        self,
        prompt: str,
        model: str,
        **kwargs
    ) -> Tuple[str, int, int, str]:
        """
        Generate video from text prompt.
        
        Args:
            prompt: The text prompt for video generation
            model: The model to use for generation
            **kwargs: Additional provider-specific parameters
            
        Returns:
            Tuple of (mime_type, width, height, filename)
        """
        pass
    
    @abstractmethod
    def get_supported_models(self) -> Dict[str, Any]:
        """
        Get list of supported models for this provider.
        
        Returns:
            Dictionary of supported models with their metadata
        """
        pass
    
    def get_supported_aspect_ratios(self) -> Dict[str, Any]:
        """
        Get list of supported aspect ratios.
        
        Returns:
            Dictionary of supported aspect ratios
        """
        return {
            "1:1": {"width": 1024, "height": 1024, "description": "Square"},
            "16:9": {"width": 1920, "height": 1080, "description": "Landscape"},
            "9:16": {"width": 1080, "height": 1920, "description": "Portrait"},
            "4:3": {"width": 1024, "height": 768, "description": "Standard"},
            "3:4": {"width": 768, "height": 1024, "description": "Portrait Standard"},
        }
