import random
import base64
import json
import time
import traceback
import os
from mimetypes import guess_type
from typing import Optional, Annotated
from pydantic import BaseModel, Field
from langchain_core.tools import tool, InjectedToolCallId
from langchain_core.runnables import RunnableConfig
import aiofiles
from nanoid import generate

from common import DEFAULT_PORT
from services.config_service import FILES_DIR
from services.db_service import db_service
from services.websocket_service import send_to_websocket, broadcast_session_update

# Import all video generators
from .video_generators import (
    ReplicateVideoGenerator,
    RunwayVideoGenerator,
    StabilityVideoGenerator,
)

# 生成唯一视频文件 ID
def generate_video_file_id():
    return 'vi_' + generate(size=8)


class GenerateVideoInputSchema(BaseModel):
    prompt: str = Field(
        description="Required. The prompt for video generation. Describe the video content you want to create.")
    aspect_ratio: str = Field(
        description="Required. Aspect ratio of the video, only these values are allowed: 1:1, 16:9, 4:3, 3:4, 9:16. Choose the best fitting aspect ratio according to the prompt.")
    duration: Optional[float] = Field(default=5.0, description="Optional; Duration in seconds for the video (2-10 seconds). Default is 5.0 seconds.")
    input_image: Optional[str] = Field(default=None, description="Optional; Image to use as reference for image-to-video generation. Pass image_id here, e.g. 'im_jurheut7.png'.")
    motion_strength: Optional[float] = Field(default=0.5, description="Optional; Motion strength for video generation (0.0-1.0). Default is 0.5.")
    tool_call_id: Annotated[str, InjectedToolCallId]


# Initialize provider instances
VIDEO_PROVIDERS = {
    'replicate': ReplicateVideoGenerator(),
    'runway': RunwayVideoGenerator(),
    'stability': StabilityVideoGenerator(),
}


@tool("generate_video",
      description="Generate a video using text prompt or optionally pass an image for image-to-video generation",
      args_schema=GenerateVideoInputSchema)
async def generate_video(
    prompt: str,
    aspect_ratio: str,
    config: RunnableConfig,
    tool_call_id: Annotated[str, InjectedToolCallId],
    duration: Optional[float] = 5.0,
    input_image: Optional[str] = None,
    motion_strength: Optional[float] = 0.5,
) -> str:
    """
    Generate a video using the specified provider.

    Args:
        prompt (str): The prompt for video generation.
        aspect_ratio (str): Aspect ratio of the video.
        config (RunnableConfig): The configuration for the runnable.
        tool_call_id (Annotated[str, InjectedToolCallId]): The ID of the tool call.
        duration (Optional[float], optional): Duration in seconds. Defaults to 5.0.
        input_image (Optional[str], optional): Input image for reference. Defaults to None.
        motion_strength (Optional[float], optional): Motion strength. Defaults to 0.5.

    Returns:
        str: The ID of the generated video.
    """
    print('🛠️ video tool_call_id', tool_call_id)
    ctx = config.get('configurable', {})
    canvas_id = ctx.get('canvas_id', '')
    session_id = ctx.get('session_id', '')
    print('🛠️ video canvas_id', canvas_id, 'session_id', session_id)
    # Inject the tool call id into the context
    ctx['tool_call_id'] = tool_call_id

    video_model = ctx.get('model_info', {}).get('video', {})
    if video_model is None:
        raise ValueError("Video model is not selected")
    model = video_model.get('model', '')
    provider = video_model.get('provider', 'replicate')

    # Get provider instance
    generator = VIDEO_PROVIDERS.get(provider)
    if not generator:
        raise ValueError(f"Unsupported video provider: {provider}")

    try:
        # Prepare input image if provided
        input_image_data = None
        if input_image:
            image_path = os.path.join(FILES_DIR, f'{input_image}')
            
            if provider == 'runway':
                # Runway needs file path
                input_image_data = image_path
            else:
                # Other providers need base64
                async with aiofiles.open(image_path, 'rb') as f:
                    image_data = await f.read()
                b64 = base64.b64encode(image_data).decode('utf-8')
                mime_type, _ = guess_type(image_path)
                if not mime_type:
                    mime_type = "image/png"
                input_image_data = f"data:{mime_type};base64,{b64}"

        # Generate video using the appropriate provider
        extra_kwargs = {}
        if provider == 'replicate':
            extra_kwargs['aspect_ratio'] = aspect_ratio
            extra_kwargs['duration'] = duration
            if input_image_data:
                extra_kwargs['input_image'] = input_image_data
        elif provider == 'runway':
            extra_kwargs['duration'] = duration
            extra_kwargs['motion_strength'] = motion_strength
            if input_image_data:
                extra_kwargs['input_image'] = input_image_data
        elif provider == 'stability':
            extra_kwargs['aspect_ratio'] = aspect_ratio
            extra_kwargs['motion_strength'] = motion_strength

        mime_type, width, height, filename = await generator.generate(
            prompt=prompt,
            model=model,
            **extra_kwargs
        )

        file_id = generate_video_file_id()
        url = f'/api/file/{filename}'

        file_data = {
            'mimeType': mime_type,
            'id': file_id,
            'dataURL': url,
            'created': int(time.time() * 1000),
        }

        new_video_element = await generate_new_video_element(canvas_id, file_id, {
            'width': width,
            'height': height,
        })

        # update the canvas data, add the new video element
        canvas_data = await db_service.get_canvas_data(canvas_id)
        if 'data' not in canvas_data:
            canvas_data['data'] = {}
        if 'elements' not in canvas_data['data']:
            canvas_data['data']['elements'] = []
        if 'files' not in canvas_data['data']:
            canvas_data['data']['files'] = {}

        canvas_data['data']['elements'].append(new_video_element)
        canvas_data['data']['files'][file_id] = file_data

        video_url = f"http://localhost:{DEFAULT_PORT}/api/file/{filename}"

        await db_service.save_canvas_data(canvas_id, json.dumps(canvas_data['data']))

        await broadcast_session_update(session_id, canvas_id, {
            'type': 'video_generated',
            'element': new_video_element,
            'file': file_data,
            'video_url': video_url,
        })

        return f"video generated successfully ![video_id: {filename}]({video_url})"

    except Exception as e:
        print(f"Error generating video: {str(e)}")
        traceback.print_exc()
        await send_to_websocket(session_id, {
            'type': 'error',
            'error': str(e)
        })
        return f"video generation failed: {str(e)}"

print('🛠️ video', generate_video.args_schema.model_json_schema())

async def generate_new_video_element(canvas_id: str, fileid: str, video_data: dict):
    canvas = await db_service.get_canvas_data(canvas_id)
    canvas_data = canvas.get('data', {})
    elements = canvas_data.get('elements', [])

    # find the last video element
    last_x = 0
    last_y = 0
    last_width = 0
    last_height = 0
    video_elements = [
        element for element in elements if element.get('type') == 'video']
    last_video_element = video_elements[-1] if len(
        video_elements) > 0 else None
    if last_video_element is not None:
        last_x = last_video_element.get('x', 0)
        last_y = last_video_element.get('y', 0)
        last_width = last_video_element.get('width', 0)
        last_height = last_video_element.get('height', 0)

    new_x = last_x + last_width + 20

    return {
        'type': 'video',
        'id': fileid,
        'x': new_x,
        'y': last_y,
        'width': video_data.get('width', 640),
        'height': video_data.get('height', 480),
        'angle': 0,
        'fileId': fileid,
        'strokeColor': '#000000',
        'fillStyle': 'solid',
        'strokeStyle': 'solid',
        'boundElements': None,
        'roundness': None,
        'frameId': None,
        'backgroundColor': 'transparent',
        'strokeWidth': 1,
        'roughness': 0,
        'opacity': 100,
        'groupIds': [],
        'seed': int(random.random() * 1000000),
        'version': 1,
        'versionNonce': int(random.random() * 1000000),
        'isDeleted': False,
        'index': None,
        'updated': 0,
        'link': None,
        'locked': False,
        'status': 'saved',
        'scale': [1, 1],
        'crop': None,
    }
